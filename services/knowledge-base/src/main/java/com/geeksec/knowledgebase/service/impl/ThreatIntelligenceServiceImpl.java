package com.geeksec.knowledgebase.service.impl;

import com.geeksec.knowledgebase.domain.entity.ThreatIntelligence;
import com.geeksec.knowledgebase.repository.mapper.ThreatIntelligenceMapper;
import com.geeksec.knowledgebase.service.ThreatIntelligenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 威胁情报服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class ThreatIntelligenceServiceImpl implements ThreatIntelligenceService {

    private final ThreatIntelligenceMapper threatIntelligenceMapper;

    @Override
    @Cacheable(value = "threat-intelligence", key = "'indicator:' + #indicator")
    public Optional<ThreatIntelligence> checkIndicator(String indicator) {
        return threatIntelligenceMapper.findByIocValue(indicator);
    }

    @Override
    @Cacheable(value = "threat-intelligence", key = "'threatName:' + #threatName")
    public Optional<ThreatIntelligence> findByThreatName(String threatName) {
        return threatIntelligenceMapper.findByThreatName(threatName);
    }

    @Override
    public List<ThreatIntelligence> findAll() {
        return threatIntelligenceMapper.selectAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "threat-intelligence", allEntries = true)
    public ThreatIntelligence create(ThreatIntelligence threatIntelligence) {
        log.info("创建威胁情报: {}", threatIntelligence.getThreatName());
        threatIntelligenceMapper.insert(threatIntelligence);
        return threatIntelligence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "threat-intelligence", allEntries = true)
    public ThreatIntelligence update(ThreatIntelligence threatIntelligence) {
        log.info("更新威胁情报: {}", threatIntelligence.getThreatName());
        threatIntelligenceMapper.update(threatIntelligence);
        return threatIntelligence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "threat-intelligence", allEntries = true)
    public void delete(Long id) {
        ThreatIntelligence threat = threatIntelligenceMapper.selectOneById(id);
        if (threat != null) {
            threat.setIsActive(false);
            threatIntelligenceMapper.update(threat);
            log.info("删除威胁情报: {}", threat.getThreatName());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "threat-intelligence", allEntries = true)
    public List<ThreatIntelligence> batchCreate(List<ThreatIntelligence> threatIntelligences) {
        log.info("批量创建威胁情报: {} 条", threatIntelligences.size());
        // 使用 MyBatis-Flex 的批量插入，性能更好
        threatIntelligenceMapper.insertBatch(threatIntelligences);
        return threatIntelligences;
    }
}
