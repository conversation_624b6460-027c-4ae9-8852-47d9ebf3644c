package com.geeksec.knowledgebase.domain.entity;

import com.geeksec.knowledgebase.domain.enums.ThreatLevel;
import com.geeksec.knowledgebase.domain.enums.ThreatType;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 威胁情报实体
 *
 * <AUTHOR>
 */
@Data
@Table("threat_intelligence")
public class ThreatIntelligence {

    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 威胁类型
     */
    @Column("threat_type")
    private ThreatType threatType;

    /**
     * 威胁名称
     */
    @Column("threat_name")
    private String threatName;

    /**
     * 威胁描述
     */
    @Column("description")
    private String description;

    /**
     * 威胁分类
     */
    @Column("category")
    private String category;

    /**
     * 威胁等级
     */
    @Column("severity")
    private ThreatLevel severity;

    /**
     * 攻击向量 (JSON格式)
     */
    @Column("attack_vectors")
    private List<String> attackVectors;

    /**
     * 影响范围
     */
    @Column("impact_scope")
    private String impactScope;

    /**
     * 检测原理
     */
    @Column("detection_principle")
    private String detectionPrinciple;

    /**
     * 技术背景
     */
    @Column("technical_background")
    private String technicalBackground;

    /**
     * 相关CVE (JSON格式)
     */
    @Column("related_cves")
    private List<String> relatedCves;

    /**
     * IOCs (Indicators of Compromise) (JSON格式)
     */
    @Column("iocs")
    private Map<String, Object> iocs;

    /**
     * IOC值 (用于快速查询的单个IOC值)
     */
    @Column("ioc_value")
    private String iocValue;

    /**
     * 置信度 (0-100)
     */
    @Column("confidence")
    private Integer confidence = 50;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 数据来源
     */
    @Column("source")
    private String source;

    /**
     * 置信度评分 (0-100)
     */
    @Column("confidence_score")
    private Integer confidenceScore = 50;

}
